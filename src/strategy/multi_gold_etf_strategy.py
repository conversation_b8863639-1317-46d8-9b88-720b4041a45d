import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Union, Tuple
import datetime
import os
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 导入缓存系统
from src.optimization.cache import (
    cache_data, get_cached_data,
    cache_regression_results, get_cached_regression_results
)

def fast_rolling_regression(y: np.ndarray, x: np.ndarray, window: int) -> tuple:
    """
    快速滚动回归计算
    返回: (beta_values, beta_std_values)
    """
    n = len(y)
    beta_values = np.full(n, np.nan)
    beta_std_values = np.full(n, np.nan)

    for i in range(window, n):
        y_window = y[i-window:i]
        x_window = x[i-window:i]

        # 计算回归系数
        variance = np.dot(x_window, x_window)
        beta = np.dot(x_window, y_window) / np.dot(x_window, x_window)

        # 计算残差和标准误差
        y_pred = beta * x_window
        residuals = y_window - y_pred
        mse = np.mean(residuals ** 2)
        beta_std = np.sqrt(mse / (variance * (window - 1)))

        beta_values[i] = beta
        beta_std_values[i] = beta_std

    return beta_values, beta_std_values

class MultiGoldETFStrategy:
    """多ETF黄金配对交易策略 - 综合监控多只黄金ETF，实现卖高买低策略"""
    
    def __init__(
        self,
        etf_codes: List[str],
        window: int = 120,
        std_dev_mult: float = 2.0,
        max_pos_size: float = 1.0,
        verbose: bool = False
    ):
        """
        初始化多ETF黄金配对交易策略
        
        Args:
            etf_codes: ETF代码列表
            window: 滚动窗口大小
            std_dev_mult: 标准差倍数
            max_pos_size: 最大持仓比例
            verbose: 是否输出详细信息
        """
        self.etf_codes = etf_codes
        self.window = window
        self.std_dev_mult = std_dev_mult
        self.max_pos_size = max_pos_size
        self.verbose = verbose
        
        # 策略状态
        self.current_position = None  # 当前持仓的ETF
        self.position_size = 0
        self.last_trade_timestamp = None
        self.last_trade_ratios = {}  # 记录每个ETF配对的最后交易比率
        
        # 数据存储
        self.price_history = None
        self.engine = None
        
        # 预计算的信号数据
        self.etf_signals = {}  # 每个ETF的交易信号
        self.etf_deviations = {}  # 每个ETF相对于基准的偏离度
        self.timestamp_to_idx = {}
        
        # 交易记录
        self.all_orders = []
        self.profit_check_log = []
        
        print(f"初始化多ETF黄金策略，包含 {len(etf_codes)} 只ETF: {etf_codes}")

    def initialize(self, engine):
        """初始化策略"""
        self.engine = engine
        self.price_history = engine.data.copy()
        
        # 验证ETF数据
        missing_etfs = [etf for etf in self.etf_codes if etf not in self.price_history.columns]
        if missing_etfs:
            raise ValueError(f"缺少以下ETF的数据: {missing_etfs}")
        
        # 只保留需要的ETF数据
        self.price_history = self.price_history[self.etf_codes]
        
        if self.verbose:
            print(f"策略初始化完成，数据范围: {self.price_history.index[0]} 到 {self.price_history.index[-1]}")
            print(f"数据形状: {self.price_history.shape}")
        
        # 预计算所有交易信号
        self._precompute_signals()

    def _precompute_signals(self):
        """预计算所有ETF的交易信号和偏离度"""
        if self.verbose:
            print("开始预计算多ETF交易信号...")
        
        # 创建时间戳索引映射
        self.timestamp_to_idx = {ts: i for i, ts in enumerate(self.price_history.index)}
        
        # 选择基准ETF（通常选择流动性最好的）
        benchmark_etf = self.etf_codes[0]  # 可以根据需要调整
        
        # 计算每个ETF相对于基准ETF的信号
        for etf_code in self.etf_codes:
            if etf_code == benchmark_etf:
                continue
                
            # 计算该ETF与基准ETF的配对信号
            etf_pair = (etf_code, benchmark_etf)
            signals, deviations = self._calculate_pair_signals(etf_code, benchmark_etf, etf_pair)
            
            self.etf_signals[etf_code] = signals
            self.etf_deviations[etf_code] = deviations
        
        if self.verbose:
            print(f"完成 {len(self.etf_signals)} 个ETF的信号预计算")

    def _calculate_pair_signals(self, etf1: str, etf2: str, etf_pair: Tuple[str, str]) -> Tuple[np.ndarray, np.ndarray]:
        """计算ETF配对的交易信号和偏离度"""
        try:
            # 缓存数据
            pair_data = self.price_history[[etf1, etf2]].dropna()
            data_hash = cache_data(pair_data, etf_pair)

            # 尝试从缓存获取滚动回归结果
            cached_regression = get_cached_regression_results(data_hash, self.window, etf_pair)

            if cached_regression is not None:
                beta_values, beta_std_values = cached_regression
                if self.verbose:
                    print(f"使用缓存的滚动回归结果 ({etf1}-{etf2}, window={self.window})")
            else:
                # 计算滚动回归
                y = np.array(pair_data[etf1].values, dtype=np.float64)
                x = np.array(pair_data[etf2].values, dtype=np.float64)
                
                beta_values, beta_std_values = fast_rolling_regression(y, x, self.window)
                cache_regression_results(data_hash, self.window, beta_values, beta_std_values, etf_pair)
                if self.verbose:
                    print(f"计算并缓存滚动回归结果 ({etf1}-{etf2}, window={self.window})")

            # 计算价格比率和偏离度
            price_ratio = pair_data[etf1] / pair_data[etf2]
            
            # 计算移动平均和标准差
            ma_values = np.full(len(pair_data), np.nan)
            std_values = np.full(len(pair_data), np.nan)
            deviations = np.full(len(pair_data), np.nan)
            
            for i in range(self.window, len(pair_data)):
                window_ratios = price_ratio.iloc[i-self.window:i]
                ma_values[i] = window_ratios.mean()
                std_values[i] = window_ratios.std()
                
                # 计算当前偏离度（标准化偏离）
                current_ratio = price_ratio.iloc[i]
                if not np.isnan(ma_values[i]) and not np.isnan(std_values[i]) and std_values[i] > 0:
                    deviations[i] = (current_ratio - ma_values[i]) / std_values[i]

            # 生成交易信号
            signals = np.zeros(len(pair_data))
            upper_threshold = self.std_dev_mult
            lower_threshold = -self.std_dev_mult
            
            for i in range(len(deviations)):
                if np.isnan(deviations[i]):
                    continue
                    
                if deviations[i] > upper_threshold:
                    signals[i] = -1  # ETF1相对高估，应该卖出ETF1
                elif deviations[i] < lower_threshold:
                    signals[i] = 1   # ETF1相对低估，应该买入ETF1
            
            return signals, deviations
            
        except Exception as e:
            print(f"计算配对信号时出错 ({etf1}-{etf2}): {str(e)}")
            return np.zeros(len(self.price_history)), np.zeros(len(self.price_history))

    def on_bar(self, timestamp: pd.Timestamp, row) -> List[Dict]:
        """
        处理每个时间点的数据 - 多ETF综合决策
        """
        orders = []
        
        # 获取当前时间点的索引
        idx = self.timestamp_to_idx.get(timestamp)
        if idx is None:
            return orders
        
        # 收集所有ETF的信号和偏离度
        etf_scores = {}
        for etf_code in self.etf_codes:
            if etf_code in self.etf_signals:
                signal = self.etf_signals[etf_code][idx] if idx < len(self.etf_signals[etf_code]) else 0
                deviation = self.etf_deviations[etf_code][idx] if idx < len(self.etf_deviations[etf_code]) else 0
                
                if not np.isnan(signal) and not np.isnan(deviation):
                    etf_scores[etf_code] = {
                        'signal': signal,
                        'deviation': deviation,
                        'abs_deviation': abs(deviation)
                    }
        
        if not etf_scores:
            return orders
        
        # 实现"卖高买低"策略逻辑
        target_etf = self._select_target_etf(etf_scores, timestamp, row)
        
        if target_etf and target_etf != self.current_position:
            # 执行持仓切换
            orders = self._execute_position_switch(target_etf, timestamp, row)
        
        return orders

    def _select_target_etf(self, etf_scores: Dict, timestamp: pd.Timestamp, row) -> Optional[str]:
        """选择目标ETF - 实现卖高买低逻辑"""
        
        # 找出所有有买入信号的ETF（相对低估）
        buy_candidates = []
        sell_candidates = []
        
        for etf_code, score in etf_scores.items():
            if score['signal'] > 0:  # 买入信号
                buy_candidates.append((etf_code, score['abs_deviation']))
            elif score['signal'] < 0:  # 卖出信号
                sell_candidates.append((etf_code, score['abs_deviation']))
        
        # 如果当前有持仓
        if self.current_position:
            # 检查当前持仓是否应该卖出
            current_score = etf_scores.get(self.current_position)
            if current_score and current_score['signal'] < 0:
                # 当前持仓应该卖出，寻找最佳买入目标
                if buy_candidates:
                    # 选择偏离度最大的买入候选
                    buy_candidates.sort(key=lambda x: x[1], reverse=True)
                    return buy_candidates[0][0]
                else:
                    # 没有买入候选，清仓
                    return None
        
        # 如果没有持仓，选择最佳买入目标
        if buy_candidates:
            buy_candidates.sort(key=lambda x: x[1], reverse=True)
            return buy_candidates[0][0]
        
        return self.current_position  # 保持当前状态

    def _execute_position_switch(self, target_etf: Optional[str], timestamp: pd.Timestamp, row) -> List[Dict]:
        """执行持仓切换"""
        orders = []
        
        # 清空当前持仓
        if self.current_position and self.position_size > 0:
            current_price = getattr(row, self.current_position, None)
            if current_price and current_price > 0:
                sell_order = {
                    'symbol': self.current_position,
                    'direction': -1,  # 卖出
                    'volume': self.position_size,
                    'price': current_price,
                    'datetime': timestamp
                }
                orders.append(sell_order)
                self.all_orders.append(sell_order)
        
        # 建立新持仓
        if target_etf:
            target_price = getattr(row, target_etf, None)
            if target_price and target_price > 0:
                # 计算新持仓规模
                available_cash = self.engine.portfolio['cash'] * self.max_pos_size
                new_size = int(available_cash / target_price)
                
                if new_size > 0:
                    buy_order = {
                        'symbol': target_etf,
                        'direction': 1,  # 买入
                        'volume': new_size,
                        'price': target_price,
                        'datetime': timestamp
                    }
                    orders.append(buy_order)
                    self.all_orders.append(buy_order)
                    
                    self.position_size = new_size
        
        # 更新持仓状态
        self.current_position = target_etf
        self.last_trade_timestamp = timestamp
        
        if self.verbose and orders:
            print(f"{timestamp}: 切换持仓到 {target_etf}, 订单数: {len(orders)}")
        
        return orders

    def save_orders_to_csv(self):
        """保存订单到CSV文件"""
        if not self.all_orders:
            return
        
        # 创建订单DataFrame
        orders_df = pd.DataFrame(self.all_orders)
        
        # 保存到文件
        etf_list_str = '_'.join(self.etf_codes[:3]) + f"_etc{len(self.etf_codes)}"  # 避免文件名过长
        start_date = self.price_history.index[0].strftime('%Y%m%d')
        end_date = self.price_history.index[-1].strftime('%Y%m%d')
        
        filename = f"orders_multi_gold_{etf_list_str}_{start_date}_{end_date}.csv"
        filepath = os.path.join('results', filename)
        
        orders_df.to_csv(filepath, index=False, encoding='utf-8')
        print(f"订单记录已保存到: {filepath}")
