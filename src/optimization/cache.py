"""
智能缓存机制
专门缓存时间序列数据和滚动回归结果，而不是完整的回测结果
这样可以在不同参数组合之间共享计算结果
"""

import os
import pickle
import hashlib
import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, Tuple
import json

class RegressionCache:
    """滚动回归结果缓存管理器"""

    def __init__(self, cache_dir: str = "cache"):
        self.cache_dir = cache_dir
        os.makedirs(cache_dir, exist_ok=True)

        # 分别缓存不同类型的数据
        self.data_cache_dir = os.path.join(cache_dir, "data")
        self.regression_cache_dir = os.path.join(cache_dir, "regression")
        os.makedirs(self.data_cache_dir, exist_ok=True)
        os.makedirs(self.regression_cache_dir, exist_ok=True)
        
    def _get_data_cache_key(self, data: pd.DataFrame, etf_pair: Optional[Tuple[str, str]] = None) -> str:
        """生成数据缓存键"""
        # 基于数据内容和ETF配对生成哈希
        etf_pair_str = f"{etf_pair[0]}_{etf_pair[1]}" if etf_pair else "default"
        data_str = f"{etf_pair_str}_{data.shape}_{data.index[0]}_{data.index[-1]}_{data.values.tobytes()}"
        return hashlib.md5(data_str.encode()).hexdigest()

    def _get_regression_cache_key(self, data_hash: str, window: int, etf_pair: Optional[Tuple[str, str]] = None) -> str:
        """生成滚动回归缓存键"""
        etf_pair_str = f"{etf_pair[0]}_{etf_pair[1]}" if etf_pair else "default"
        cache_str = f"{etf_pair_str}_{data_hash}_{window}"
        return hashlib.md5(cache_str.encode()).hexdigest()

    def cache_data(self, data: pd.DataFrame, etf_pair: Optional[Tuple[str, str]] = None) -> str:
        """缓存时间序列数据"""
        data_hash = self._get_data_cache_key(data, etf_pair)
        cache_file = os.path.join(self.data_cache_dir, f"{data_hash}.pkl")

        if not os.path.exists(cache_file):
            try:
                with open(cache_file, 'wb') as f:
                    pickle.dump({
                        'data': data,
                        'hash': data_hash,
                        'etf_pair': etf_pair
                    }, f)
            except Exception as e:
                print(f"数据缓存保存失败: {e}")

        return data_hash

    def get_cached_data(self, data_hash: str) -> Optional[pd.DataFrame]:
        """获取缓存的时间序列数据"""
        cache_file = os.path.join(self.data_cache_dir, f"{data_hash}.pkl")

        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'rb') as f:
                    cached = pickle.load(f)
                    return cached['data']
            except Exception:
                os.remove(cache_file)

        return None
    
    def cache_regression_results(self, data_hash: str, window: int,
                               beta_values: np.ndarray, beta_std_values: np.ndarray,
                               etf_pair: Optional[Tuple[str, str]] = None) -> None:
        """缓存滚动回归结果"""
        regression_hash = self._get_regression_cache_key(data_hash, window, etf_pair)
        cache_file = os.path.join(self.regression_cache_dir, f"{regression_hash}.pkl")

        try:
            with open(cache_file, 'wb') as f:
                pickle.dump({
                    'beta_values': beta_values,
                    'beta_std_values': beta_std_values,
                    'window': window,
                    'data_hash': data_hash,
                    'etf_pair': etf_pair
                }, f)
        except Exception as e:
            print(f"回归结果缓存保存失败: {e}")

    def get_cached_regression_results(self, data_hash: str, window: int,
                                    etf_pair: Optional[Tuple[str, str]] = None) -> Optional[Tuple[np.ndarray, np.ndarray]]:
        """获取缓存的滚动回归结果"""
        regression_hash = self._get_regression_cache_key(data_hash, window, etf_pair)
        cache_file = os.path.join(self.regression_cache_dir, f"{regression_hash}.pkl")

        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'rb') as f:
                    cached = pickle.load(f)
                    return cached['beta_values'], cached['beta_std_values']
            except Exception:
                os.remove(cache_file)

        return None
    
    def clear(self) -> None:
        """清空所有缓存"""
        for cache_dir in [self.data_cache_dir, self.regression_cache_dir]:
            for filename in os.listdir(cache_dir):
                if filename.endswith('.pkl'):
                    os.remove(os.path.join(cache_dir, filename))

    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        data_files = len([f for f in os.listdir(self.data_cache_dir) if f.endswith('.pkl')])
        regression_files = len([f for f in os.listdir(self.regression_cache_dir) if f.endswith('.pkl')])

        return {
            'data_cache_size': data_files,
            'regression_cache_size': regression_files,
            'total_cache_size': data_files + regression_files,
            'cache_dir': self.cache_dir
        }

def get_data_hash(data: pd.DataFrame) -> str:
    """计算数据的哈希值"""
    return hashlib.md5(str(data.values.tobytes()).encode()).hexdigest()

# 全局缓存实例
_global_cache = RegressionCache()

def cache_data(data: pd.DataFrame, etf_pair: Optional[Tuple[str, str]] = None) -> str:
    """缓存时间序列数据的便捷函数"""
    return _global_cache.cache_data(data, etf_pair)

def get_cached_data(data_hash: str) -> Optional[pd.DataFrame]:
    """获取缓存数据的便捷函数"""
    return _global_cache.get_cached_data(data_hash)

def cache_regression_results(data_hash: str, window: int,
                           beta_values: np.ndarray, beta_std_values: np.ndarray,
                           etf_pair: Optional[Tuple[str, str]] = None) -> None:
    """缓存滚动回归结果的便捷函数"""
    _global_cache.cache_regression_results(data_hash, window, beta_values, beta_std_values, etf_pair)

def get_cached_regression_results(data_hash: str, window: int,
                                etf_pair: Optional[Tuple[str, str]] = None) -> Optional[Tuple[np.ndarray, np.ndarray]]:
    """获取缓存的滚动回归结果的便捷函数"""
    return _global_cache.get_cached_regression_results(data_hash, window, etf_pair)

def clear_cache() -> None:
    """清空缓存的便捷函数"""
    _global_cache.clear()

def get_cache_info() -> Dict[str, Any]:
    """获取缓存信息"""
    return _global_cache.get_cache_info()
