"""
测试新的智能缓存机制
验证滚动回归结果缓存和on_bar方法优化效果
"""

import time
import numpy as np
import pandas as pd
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.data.loader import DataLoader
from src.optimization.optimizer import StrategyOptimizer
from src.optimization.cache import clear_cache, get_cache_info
from src.engine.engine import BacktestEngine
from src.strategy.pairs_strategy import PairsStrategy

BACKTEST_PARAMS = {
    'start_date': '2022-11-27',
    'end_date': '2024-11-25',
    'initial_capital': 100000,
    'data_frequency': 'minute',  # 'day', 'minute', 'tick'
    'commission_rate': 0.00005,
    'slippage_rate': 0.001
}

def create_test_data():
    # 初始化数据加载器
    data_loader = DataLoader()
    
    # 加载回测数据（只加载一次）
    print("正在加载数据...")
    data = data_loader.load_data(
        start_date=BACKTEST_PARAMS['start_date'],
        end_date=BACKTEST_PARAMS['end_date'],
        frequency=BACKTEST_PARAMS['data_frequency']
    )
    print(f"数据加载完成，范围: {data.index[0]} 到 {data.index[-1]}")
    
    return data

def test_cache_effectiveness():
    """测试缓存机制的有效性"""
    print("=== 测试缓存机制有效性 ===")
    
    # 清空缓存
    clear_cache()
    print("已清空缓存")
    
    data = create_test_data()
    print(f"创建测试数据: {len(data)} 个时间点")
    
    backtest_params = {
        'initial_capital': 1000000,
        'commission_rate': 0.0003,
        'slippage_rate': 0.0001
    }
    
    # 测试相同window的不同参数组合
    param_sets = [
        {'window': 60, 'std_dev_mult': 1.5, 'max_pos_size': 1.0},
        {'window': 60, 'std_dev_mult': 2.0, 'max_pos_size': 1.0},
        {'window': 60, 'std_dev_mult': 2.5, 'max_pos_size': 1.0},
        {'window': 90, 'std_dev_mult': 2.0, 'max_pos_size': 1.0},
        {'window': 90, 'std_dev_mult': 2.5, 'max_pos_size': 1.0},
    ]
    
    print(f"\n测试 {len(param_sets)} 组参数...")
    
    times = []
    for i, params in enumerate(param_sets):
        print(f"\n--- 参数组 {i+1}: {params} ---")
        
        start_time = time.time()
        
        strategy = PairsStrategy(**params, verbose=True)
        engine = BacktestEngine(
            strategy=strategy,
            data=data,
            initial_capital=backtest_params['initial_capital'],
            commission_rate=backtest_params['commission_rate'],
            slippage_rate=backtest_params['slippage_rate'],
            verbose=False
        )
        results = engine.run()
        
        end_time = time.time()
        elapsed = end_time - start_time
        times.append(elapsed)
        
        print(f"耗时: {elapsed:.3f}秒")
        print(f"收益率: {((results['equity_curve'][-1] / results['equity_curve'][0]) - 1) * 100:.2f}%")
        print(f"交易次数: {len(results['trades'])}")
        
        # 显示缓存信息
        cache_info = get_cache_info()
        print(f"缓存状态: {cache_info}")
    
    print(f"\n=== 缓存效果分析 ===")
    print(f"平均单次回测时间: {np.mean(times):.3f}秒")
    print(f"首次回测时间: {times[0]:.3f}秒")
    print(f"后续回测平均时间: {np.mean(times[1:]):.3f}秒")
    
    if len(times) > 1:
        speedup = times[0] / np.mean(times[1:])
        print(f"缓存加速比: {speedup:.2f}x")
    
    final_cache_info = get_cache_info()
    print(f"最终缓存信息: {final_cache_info}")

def test_onbar_performance():
    """测试on_bar方法的性能"""
    print("\n=== 测试on_bar方法性能 ===")
    
    data = create_test_data()  # 更大的数据集
    print(f"数据大小: {len(data)} 个时间点")
    
    params = {
        'window': 60,
        'std_dev_mult': 2.0,
        'max_pos_size': 1.0
    }
    
    # 测试多次运行
    n_runs = 3
    times = []
    
    for i in range(n_runs):
        print(f"\n第 {i+1} 次运行:")
        
        start_time = time.time()
        
        strategy = PairsStrategy(**params, verbose=False)
        engine = BacktestEngine(
            strategy=strategy,
            data=data,
            initial_capital=1000000,
            commission_rate=0.0003,
            slippage_rate=0.0001,
            verbose=False
        )
        results = engine.run()
        
        end_time = time.time()
        elapsed = end_time - start_time
        times.append(elapsed)
        
        print(f"总耗时: {elapsed:.3f}秒")
        print(f"平均每个时间点: {elapsed/len(data)*1000:.3f}毫秒")
        print(f"交易次数: {len(results['trades'])}")
    
    avg_time = np.mean(times)
    print(f"\n平均回测时间: {avg_time:.3f}秒")
    print(f"平均每个时间点处理时间: {avg_time/len(data)*1000:.3f}毫秒")

def test_parameter_optimization_with_cache():
    """测试带缓存的参数优化"""
    print("\n=== 测试带缓存的参数优化 ===")
    
    # 清空缓存
    clear_cache()
    
    data = create_test_data()
    print(f"数据大小: {len(data)} 个时间点")
    
    backtest_params = {
        'initial_capital': 1000000,
        'commission_rate': 0.0003,
        'slippage_rate': 0.0001
    }
    
    # 设计参数范围，让多个组合共享相同的window
    param_ranges = {
        'window': [60, 90],  # 只有2个window值
        'std_dev_mult': [1.5, 2.0, 2.5, 3.0],  # 4个std_dev_mult值
        'max_pos_size': [1.0]
    }
    
    total_combinations = np.prod([len(v) for v in param_ranges.values()])
    print(f"参数组合数量: {total_combinations}")
    print("预期缓存效果: 多个参数组合将共享相同window的滚动回归结果")
    
    optimizer = StrategyOptimizer(data, backtest_params)
    
    start_time = time.time()
    best_params, best_results, all_results = optimizer.optimize(
        param_ranges=param_ranges,
        n_jobs=1,  # 使用单进程以便观察缓存效果
        metric='total_returns'
    )
    total_time = time.time() - start_time
    
    print(f"\n优化完成!")
    print(f"总耗时: {total_time:.1f}秒")
    print(f"平均每组参数: {total_time/total_combinations:.3f}秒")
    print(f"最优参数: {best_params}")
    
    final_equity = best_results['equity_curve'][-1]
    total_return = (final_equity / backtest_params['initial_capital'] - 1) * 100
    print(f"最优收益率: {total_return:.2f}%")
    
    # 显示缓存信息
    cache_info = get_cache_info()
    print(f"缓存信息: {cache_info}")

def main():
    """主函数"""
    print("开始智能缓存机制测试...")
    print(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试缓存有效性
    test_cache_effectiveness()
    
    # 测试on_bar性能
    test_onbar_performance()
    
    # 测试参数优化中的缓存效果
    test_parameter_optimization_with_cache()
    
    print("\n" + "="*50)
    print("智能缓存测试完成!")

if __name__ == "__main__":
    main()
